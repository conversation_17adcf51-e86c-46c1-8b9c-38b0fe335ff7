/**
 * 其他车辆出厂功能测试
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { renderHook, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, jest, beforeEach } from '@jest/globals';

import VehicleManagementModal from '../../models/VehicleManagementModal';
import { useOtherVehicleExitModal, type OtherVehicleExitData } from '../../features/task-management/hooks/useOtherVehicleExitModal';

// Mock toast hook
jest.mock('@/shared/hooks/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn(),
  }),
}));

// 测试组件包装器
function TestWrapper() {
  const modal = useOtherVehicleExitModal({
    onExitConfirm: jest.fn<(data: OtherVehicleExitData) => void>(),
  });

  return (
    <div>
      <button onClick={modal.openModal}>打开其他车辆出厂</button>
      <VehicleManagementModal
        isOpen={modal.isOpen}
        onOpenChangeAction={modal.closeModal}
        onConfirm={modal.handleConfirm}
      />
    </div>
  );
}

describe('其他车辆出厂功能', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('应该能够打开模态框', async () => {
    const user = userEvent.setup();
    render(<TestWrapper />);

    // 点击打开按钮
    const openButton = screen.getByText('打开其他车辆出厂');
    await user.click(openButton);

    // 验证模态框是否打开
    expect(screen.getByText('其他车辆出厂')).toBeInTheDocument();
    expect(screen.getByLabelText('车号')).toBeInTheDocument();
    expect(screen.getByLabelText('司机')).toBeInTheDocument();
    expect(screen.getByLabelText('原因')).toBeInTheDocument();
  });

  it('应该能够输入表单数据', async () => {
    const user = userEvent.setup();
    render(<TestWrapper />);

    // 打开模态框
    await user.click(screen.getByText('打开其他车辆出厂'));

    // 输入表单数据
    const vehicleInput = screen.getByPlaceholderText('请输入或选择车号');
    const driverInput = screen.getByPlaceholderText('请输入或选择司机姓名');
    const reasonInput = screen.getByPlaceholderText('请输入或选择出厂原因');

    await user.type(vehicleInput, '苏A12345');
    await user.type(driverInput, '张师傅');
    await user.type(reasonInput, '维修保养');

    expect(vehicleInput).toHaveValue('苏A12345');
    expect(driverInput).toHaveValue('张师傅');
    expect(reasonInput).toHaveValue('维修保养');
  });

  it('应该验证必填字段', async () => {
    const user = userEvent.setup();
    render(<TestWrapper />);

    // 打开模态框
    await user.click(screen.getByText('打开其他车辆出厂'));

    // 直接点击确定按钮（不填写任何字段）
    const confirmButton = screen.getByText('确定');
    await user.click(confirmButton);

    // 验证是否显示验证错误（通过toast）
    // 注意：这里我们无法直接测试toast，但可以验证模态框没有关闭
    expect(screen.getByText('其他车辆出厂')).toBeInTheDocument();
  });

  it('应该能够取消操作', async () => {
    const user = userEvent.setup();
    render(<TestWrapper />);

    // 打开模态框
    await user.click(screen.getByText('打开其他车辆出厂'));

    // 点击取消按钮
    const cancelButton = screen.getByText('取消');
    await user.click(cancelButton);

    // 验证模态框是否关闭
    expect(screen.queryByText('其他车辆出厂')).not.toBeInTheDocument();
  });

  it('应该能够完成完整的出厂流程', async () => {
    const user = userEvent.setup();
    const mockOnConfirm = jest.fn<(data: OtherVehicleExitData) => void>();

    function TestWrapperWithMock() {
      const modal = useOtherVehicleExitModal({
        onExitConfirm: mockOnConfirm,
      });

      return (
        <div>
          <button onClick={modal.openModal}>打开其他车辆出厂</button>
          <VehicleManagementModal
            isOpen={modal.isOpen}
            onOpenChangeAction={modal.closeModal}
            onConfirm={modal.handleConfirm}
          />
        </div>
      );
    }

    render(<TestWrapperWithMock />);

    // 打开模态框
    await user.click(screen.getByText('打开其他车辆出厂'));

    // 填写表单
    await user.type(screen.getByPlaceholderText('请输入或选择车号'), '苏A12345');
    await user.type(screen.getByPlaceholderText('请输入或选择司机姓名'), '张师傅');
    await user.type(screen.getByPlaceholderText('请输入或选择出厂原因'), '维修保养');

    // 点击确定
    await user.click(screen.getByText('确定'));

    // 验证回调是否被调用
    await waitFor(() => {
      expect(mockOnConfirm).toHaveBeenCalledWith({
        vehicleNumber: '苏A12345',
        driverName: '张师傅',
        reason: '维修保养',
      });
    });
  });
});

describe('useOtherVehicleExitModal Hook', () => {
  it('应该提供正确的初始状态', () => {
    const { result } = renderHook(() => useOtherVehicleExitModal());

    expect(result.current.isOpen).toBe(false);
    expect(typeof result.current.openModal).toBe('function');
    expect(typeof result.current.closeModal).toBe('function');
    expect(typeof result.current.handleConfirm).toBe('function');
  });

  it('应该能够打开和关闭模态框', () => {
    const { result } = renderHook(() => useOtherVehicleExitModal());

    // 初始状态应该是关闭的
    expect(result.current.isOpen).toBe(false);

    // 打开模态框
    act(() => {
      result.current.openModal();
    });
    expect(result.current.isOpen).toBe(true);

    // 关闭模态框
    act(() => {
      result.current.closeModal();
    });
    expect(result.current.isOpen).toBe(false);
  });
});
